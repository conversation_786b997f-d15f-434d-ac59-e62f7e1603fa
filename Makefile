#APP_NAMES := migrate backend frontend
APP_NAMES := migrate backend frontend
DOCKER_REPO := warda 

.PHONY: all build docker-build docker-push helm-deploy helm-delete minikube-setup minikube-build keycloak-setup keycloak-validate keycloak-hosts

minikube-init:
	@echo "🔧 Ensuring Helm is ready and charts are pulled..."
	helm repo add grafana https://grafana.github.io/helm-charts || true
	helm repo update
	helm plugin install https://github.com/chartmuseum/helm-push || true

	@echo "🚀 Starting Minikube..."
	minikube start --driver=docker
	minikube addons enable ingress || true

	@echo "📦 Updating Helm dependencies..."
	cd helm/warda && helm dependency update

	@echo "✅ Minikube init complete. You can now run 'make minikube-build'"

# ---- Build All Rust Apps ----
build:
	cargo build --workspace --release

# ---- Minikube Setup ----
minikube-start:
	@if ! minikube status | grep -q "Running"; then \
		echo "🚀 Starting Minikube..."; \
		eval $(minikube -p minikube docker-env) minikube start --driver=docker; \
		eval $(minikube -p minikube docker-env) minikube addons enable ingress || true \
	else \
		echo "✅ Minikube already running."; \
	fi

minikube-build: minikube-start
	@echo "🐳 Building Docker images in Minikube's Docker daemon..."
	@eval $$(minikube -p minikube docker-env) && \
	for app in $(APP_NAMES); do \
		echo "🛠️  Building $$app..."; \
		docker build \
			-f apps/$$app/Dockerfile \
		    --build-arg ROOT=../.. \
			-t warda/$$app:latest \
			. && \
		echo "✅ Built warda/$$app:latest in Minikube's Docker daemon"; \
	done
	@echo "📋 Available images in Minikube's Docker daemon:"
	@eval $$(minikube -p minikube docker-env) && docker images | grep -E 'warda/|REPOSITORY' || echo "No warda images found"

# ---- Build and Deploy Without Restarting Minikube ----
minikube-build-deploy: minikube-build
	$(MAKE) helm-deploy

# ---- Helm Deploy ----
helm-deploy:
	helm upgrade --install warda helm/warda -f helm/warda/values.yaml
	@for app in $(APP_NAMES); do \
		kubectl rollout restart deployment $$app || true ; \
	done

# ---- Helm Teardown ----
helm-delete:
	helm uninstall warda

# ---- Minikube Dashboard ----
dashboard:
	minikube dashboard

# ---- Open Grafana via Minikube ----
grafana:
	@echo "🚀 Opening Grafana UI via Minikube..."
	minikube service warda-grafana

# ---- Local Docker Builds ----
docker-build:

# ---- Push (optional, for remote clusters) ----
docker-push:
	@for app in $(APP_NAMES); do \
		docker tag $(DOCKER_REPO)/$$app:latest $(DOCKER_REPO)/$$app:latest ; \
		docker push $(DOCKER_REPO)/$$app:latest ; \
	done

# ---- Open Key Services via Minikube ----
minikube-service:
	minikube dashboard & \
	minikube service backend warda-grafana

# ---- Keycloak Setup ----
keycloak-setup: minikube-build-deploy
	@echo "🔐 Setting up Keycloak..."
	@echo "📡 Enabling ingress addon..."
	@minikube addons enable ingress || true
	@echo "⏳ Waiting for Keycloak to be ready..."
	@kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=keycloak --timeout=300s || true
	@echo "✅ Keycloak setup complete!"
	@echo ""
	@$(MAKE) keycloak-hosts
	@echo ""
	@echo "🎯 Access Keycloak at: http://keycloak.local"
	@echo "   Username: admin"
	@echo "   Password: admin123"

keycloak-hosts:
	@echo "📋 Add these entries to your /etc/hosts file:"
	@echo "$$(minikube ip) keycloak.local"
	@echo "$$(minikube ip) frontend.local"
	@echo "$$(minikube ip) backend.local"
	@echo ""
	@echo "💡 Quick command to add them:"
	@echo "echo '$$(minikube ip) keycloak.local' | sudo tee -a /etc/hosts"
	@echo "echo '$$(minikube ip) frontend.local' | sudo tee -a /etc/hosts"
	@echo "echo '$$(minikube ip) backend.local' | sudo tee -a /etc/hosts"

keycloak-validate:
	@echo "🔍 Validating Keycloak setup..."
	@echo "📋 Checking pods:"
	@kubectl get pods -l app.kubernetes.io/name=keycloak
	@echo ""
	@echo "📋 Checking services:"
	@kubectl get svc -l app.kubernetes.io/name=keycloak
	@echo ""
	@echo "📋 Checking ingress:"
	@kubectl get ingress
	@echo ""
	@echo "🌐 Minikube IP: $$(minikube ip)"
	@echo ""
	@if curl -s -o /dev/null -w "%{http_code}" http://keycloak.local/realms/master | grep -q "200"; then \
		echo "✅ Keycloak is accessible at http://keycloak.local"; \
	else \
		echo "❌ Keycloak is not accessible. Try:"; \
		echo "   kubectl port-forward svc/warda-keycloak 8080:8080"; \
	fi

keycloak-port-forward:
	@echo "🔗 Port forwarding Keycloak to localhost:8080..."
	kubectl port-forward svc/warda-keycloak 8080:8080

keycloak-logs:
	@echo "📋 Keycloak logs:"
	kubectl logs -l app.kubernetes.io/name=keycloak --tail=50

print-hosts:
	@echo "\nAdd these entries to your /etc/hosts file (if not present):"
	@echo "$$([ -x "$(shell command -v minikube 2>/dev/null)" ] && minikube ip || echo '<MINIKUBE_IP>') frontend.local keycloak.local"
	@echo "\nExample:"
	@echo "************ frontend.local keycloak.local"
	@echo ""
	@echo "Access the app at:    http://frontend.local"
	@echo "Access Keycloak at:   http://keycloak.local"

onboard: minikube-init minikube-build-deploy print-hosts
	@echo "\nSetup complete! Follow the instructions above to finish configuring your local environment."

DOCKER ?= docker
TAG ?= latest
REGISTRY ?= ghcr.io/sietse-doubleprecision
APPS := migrate backend frontend

all: migrate backend frontend

migrate:
	docker build -f apps/migrate/Dockerfile .

backend:
	@echo "🔧 Building backend with optimized caching..."
	@export DOCKER_BUILDKIT=1 && docker build \
		--file apps/backend/Dockerfile \
		--tag warda-backend \
		--cache-from warda-backend:latest \
		--build-arg BUILDKIT_INLINE_CACHE=1 \
		.

frontend:
	@echo "🎨 Building frontend with optimized caching..."
	@export DOCKER_BUILDKIT=1 && docker build \
		--file apps/frontend/Dockerfile \
		--tag warda-frontend \
		--cache-from warda-frontend:latest \
		--build-arg BUILDKIT_INLINE_CACHE=1 \
		.

frontend-docker:
	@echo "🎨 Building frontend (legacy target)..."
	@export DOCKER_BUILDKIT=1 && docker build \
		--file apps/frontend/Dockerfile \
		--tag frontend \
		--cache-from frontend:latest \
		--build-arg BUILDKIT_INLINE_CACHE=1 \
		.

# Build all services with optimized caching
build-all:
	@echo "🚀 Building all services with optimized caching..."
	@./docker-build-optimized.sh

# Clean Docker cache to force fresh builds
docker-clean:
	@echo "🧹 Cleaning Docker cache..."
	@docker system prune -f
	@docker builder prune -f

migrate-docker:
	cargo build --package migrate

backend-docker:
	cargo build --package backend

frontend-docker-old:
	cargo build --package frontend