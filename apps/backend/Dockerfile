FROM rust:1.88.0-slim-bookworm AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y pkg-config libssl-dev gcc-x86-64-linux-gnu && rm -rf /var/lib/apt/lists/*

# Install target (cached layer)
RUN rustup target add x86_64-unknown-linux-gnu

ENV CARGO_TARGET_X86_64_UNKNOWN_LINUX_GNU_LINKER=x86_64-linux-gnu-gcc

WORKDIR /app

# Copy workspace manifests first (for dependency caching)
COPY Cargo.toml Cargo.lock ./
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/frontend/Cargo.toml ./apps/frontend/
COPY apps/migrate/Cargo.toml ./apps/migrate/
COPY crates/shared/Cargo.toml ./crates/shared/

# Create dummy source files to build dependencies
RUN mkdir -p apps/backend/src apps/frontend/src apps/migrate/src crates/shared/src && \
    echo "fn main() {}" > apps/backend/src/main.rs && \
    echo "fn main() {}" > apps/frontend/src/main.rs && \
    echo "pub fn hello() {}" > apps/frontend/src/lib.rs && \
    echo "fn main() {}" > apps/migrate/src/main.rs && \
    echo "pub fn hello() {}" > crates/shared/src/lib.rs

# Build dependencies only (cached layer)
RUN cargo build --release --package backend --target x86_64-unknown-linux-gnu
RUN rm -rf apps/backend/src crates/shared/src

# Copy actual source code (keep dummy files for other workspace members)
COPY apps/backend/src ./apps/backend/src/
COPY crates/shared/src ./crates/shared/src/

# Build the actual application
RUN cargo build --release --package backend --target x86_64-unknown-linux-gnu

FROM debian:bookworm-slim AS runtime

RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/x86_64-unknown-linux-gnu/release/backend /usr/local/bin/

ENTRYPOINT ["/usr/local/bin/backend"]