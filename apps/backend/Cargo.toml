[package]
name = "backend"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = "0.8.4"
serde_json = { version = "1.0.141" }
tokio = { version = "1.0", features = ["full"] }
tower = { version = "0.5.2", features = ["util", "timeout"] }
tower-http = { version = "0.6.1", features = ["add-extension", "trace"] }
tracing = "0.1"
chrono = "0.4.41"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
shared = { path = "../../crates/shared" }
