FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev gcc-x86-64-linux-gnu && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY . .

RUN rustup target add x86_64-unknown-linux-gnu

ENV CARGO_TARGET_X86_64_UNKNOWN_LINUX_GNU_LINKER=x86_64-linux-gnu-gcc

RUN cargo build --release --package migrate --target x86_64-unknown-linux-gnu

FROM debian:bookworm-slim AS runtime

RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/x86_64-unknown-linux-gnu/release/migrate /usr/local/bin/

ENTRYPOINT ["/usr/local/bin/migrate"]