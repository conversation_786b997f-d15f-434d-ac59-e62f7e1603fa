apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-backend
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app.kubernetes.io/component: backend
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: backend
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: backend
        app.kubernetes.io/instance: {{ .Release.Name }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ .Release.Name }}-backend
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: wait-for-db
          image: "{{ .Values.waitContainer.image }}:{{ .Values.waitContainer.tag }}"
          command: 
            - 'sh'
            - '-c'
            - |
              # Install required tools
              apk add --no-cache postgresql-client
              
              # Wait for PostgreSQL to accept connections
              echo "Waiting for PostgreSQL at {{ .Release.Name }}-postgresql:5432..."
              until pg_isready -h {{ .Release.Name }}-postgresql -p 5432 -U postgres; do
                echo "PostgreSQL not yet accepting connections, retrying...";
                sleep 2;
              done
              echo "PostgreSQL is ready!"
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: postgres-password
      containers:
        - name: backend
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
          readinessProbe:
            httpGet:
              path: /health
              port: http
          env:
            - name: RUST_LOG
              value: "{{ .Values.env.RUST_LOG }}"
            - name: CONFIG_PATH
              value: "/config/config.toml"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: database-url
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: postgres-password
            {{- if .Values.oauth }}
            - name: KEYCLOAK_URL
              value: "{{ .Values.oauth.keycloak.url }}"
            - name: KEYCLOAK_REALM
              value: "{{ .Values.oauth.keycloak.realm }}"
            - name: KEYCLOAK_CLIENT_ID
              value: "{{ .Values.oauth.keycloak.clientId }}"
            - name: KEYCLOAK_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-keycloak-secrets
                  key: BACKEND_CLIENT_SECRET
            - name: KEYCLOAK_ISSUER
              value: "{{ .Values.oauth.keycloak.issuer }}"
            {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: config-volume
              mountPath: /config
              readOnly: true
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: config-volume
          configMap:
            name: {{ .Release.Name }}-backend-config