name: CI - Deploy Dev Cluster

on:
  push:
    branches: [dev]

jobs:
  build-and-push:
    runs-on: wardaserver
    strategy:
      matrix:
        app: [migrate, backend, frontend]

    env:
      IMAGE_PREFIX: ghcr.io/${{ github.repository_owner }}/warda

    steps:
      - name: Set KUBECONFIG env
        run: echo "KUBECONFIG=/home/<USER>/.kube/config" >> $GITHUB_ENV

      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - uses: docker/setup-buildx-action@v3.11.1
        with:
          driver-opts: image=moby/buildkit:v0.23.2

      - uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GHCR_PAT }}

      - name: Build and push ${{ matrix.app }}
        run: |
          echo "🛠️ Building and pushing ${{ matrix.app }}..."
          docker buildx build \
            --file apps/${{ matrix.app }}/Dockerfile \
            --tag $IMAGE_PREFIX/${{ matrix.app }}:dev \
            --cache-from type=local,src=/tmp/.buildx-cache \
            --cache-to type=local,dest=/tmp/.buildx-cache \
            --push \
            .

  deploy:
    runs-on: wardaserver
    needs: build-and-push

    env:
      IMAGE_PREFIX: ghcr.io/${{ github.repository_owner }}/warda

    steps:
      - name: Set KUBECONFIG env
        run: echo "KUBECONFIG=/home/<USER>/.kube/config" >> $GITHUB_ENV

      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - uses: azure/setup-helm@v4.3.0
        with:
          version: 'v3.18.4'

      - name: Helm deploy
        run: |
          helm upgrade --install warda helm/warda \
            --namespace warda \
            --create-namespace \
            --values helm/warda/values.yaml \
            --set image.backend.repository=$IMAGE_PREFIX/backend \
            --set image.backend.tag=dev \
            --set image.frontend.repository=$IMAGE_PREFIX/frontend \
            --set image.frontend.tag=dev \
            --set image.migrate.repository=$IMAGE_PREFIX/migrate \
            --set image.migrate.tag=dev \
            --timeout 10m \
            --wait
